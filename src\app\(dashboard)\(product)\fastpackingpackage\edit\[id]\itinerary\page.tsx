'use client'

import { useParams } from 'next/navigation'
import { useState } from 'react'
import { ItineraryItem } from '@/types/package-form'
import { AddItineraryForm } from '@/modules/product/component/add-itinerary-form'
import { ItineraryList } from '@/modules/product/component/itinerary-list'

export default function EditItineraryPage() {
  const params = useParams()
  const packageId = params.id

  const [items, setItems] = useState<ItineraryItem[]>([])
  const [editingItem, setEditingItem] = useState<ItineraryItem | null>(null)

  const handleAddItinerary = (newItem: Omit<ItineraryItem, 'id'>) => {
    setItems(prev => [...prev, { ...newItem, id: Date.now() }])
  }
  const handleUpdateItinerary = (updatedItem: ItineraryItem) => {
    setItems(prev => prev.map(item => (item.id === updatedItem.id ? updatedItem : item)))
    setEditingItem(null)
  }
  const handleDeleteItinerary = (id: number) => {
    setItems(prev => prev.filter(i => i.id !== id))
  }
  const handleEditItinerary = (id: number) => {
    const found = items.find(i => i.id === id) || null
    setEditingItem(found)
  }
  const handleCancelEdit = () => setEditingItem(null)

  return (
    <div>
      <EditTabs packageId={packageId} />
      <div className="grid grid-cols-2 gap-4">
        <AddItineraryForm
          editingItem={editingItem}
          onAddItinerary={handleAddItinerary}
          onUpdateItinerary={handleUpdateItinerary}
          onCancelEdit={handleCancelEdit}
        />
        <ItineraryList items={items} onEdit={handleEditItinerary} onDelete={handleDeleteItinerary} />
      </div>
    </div>
  )
}
