'use client'

import { useRouter } from 'next/navigation'
import PackagePage from '@/modules/product/template/package-page'
import { use } from 'react'

interface EditFastPackingPackagePageProps {
  params: Promise<{ id: string }>
}

export default function EditFastPackingPackagePage({ params }: EditFastPackingPackagePageProps) {
  const router = useRouter()

  const { id } = use(params)
  const packageId: number = parseInt(id)

  const handleCancel = (): void => {
    router.push(`/fastpackingpackage`)
  }

  return (
    <PackagePage
      mode="edit"
      category="fastpackingpackage"
      packageId={packageId}
      // initialData={demoPackageData}
      // onSave={handleSave}
      onCancel={handleCancel}
    />
  )
}
